TASK [../roles/bundler_main : setup_haproxy | Restart haproxy service] *************************************************************************
fatal: [localhost]: FAILED! => {"changed": true, "cmd": ["haproxy", "-f", "/etc/haproxy/haproxy.cfg"], "delta": "0:00:00.069956", "end": "2025-09-04 15:09:50.618202", "msg": "non-zero return code", "rc": 1, "start": "2025-09-04 15:09:50.548246", "stderr": "[WARNING]  (115608) : parsing [/etc/haproxy/haproxy.cfg:25]: 'log-format' overrides previous 'option httplog' in 'defaults' section.\n[NOTICE]   (115608) : haproxy version is 2.4.22-f8e3218\n[NOTICE]   (115608) : path to executable is /sbin/haproxy\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:87] : 'server docker.io' : could not resolve address 'docker-io-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:93] : 'server registry-1.docker.io' : could not resolve address 'docker-io-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:99] : 'server gcr.io' : could not resolve address 'gcr-io-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:105] : 'server ghcr.io' : could not resolve address 'ghcr-io-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:111] : 'server k8s.gcr.io' : could not resolve address 'k8s-gcr-io-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:117] : 'server icr.io' : could not resolve address 'icr-io-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:123] : 'server cp.icr.io' : could not resolve address 'cp-icr-io-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:129] : 'server nvcr.io' : could not resolve address 'nvcr-io-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:135] : 'server quay.io' : could not resolve address 'quay-io-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:141] : 'server registry.access.redhat.com' : could not resolve address 'redhat-access-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:147] : 'server registry.connect.redhat.com' : could not resolve address 'redhat-connect-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:153] : 'server registry.gitlab.com' : could not resolve address 'gitlab-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:159] : 'server registry.redhat.io' : could not resolve address 'redhat-io-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:165] : 'server registry.k8s.io' : could not resolve address 'k8s-io-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:171] : 'server registry.ci.openshift.org' : could not resolve address 'registry-ci-openshift-org-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:177] : 'server harbor.us.lmco.com' : could not resolve address 'harbor-us-nexus.'.\n[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:183] : 'server harbor.global.lmco.com' : could not resolve address 'harbor-global-nexus.'.\n[ALERT]    (115608) : Failed to initialize server(s) addr.", "stderr_lines": ["[WARNING]  (115608) : parsing [/etc/haproxy/haproxy.cfg:25]: 'log-format' overrides previous 'option httplog' in 'defaults' section.", "[NOTICE]   (115608) : haproxy version is 2.4.22-f8e3218", "[NOTICE]   (115608) : path to executable is /sbin/haproxy", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:87] : 'server docker.io' : could not resolve address 'docker-io-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:93] : 'server registry-1.docker.io' : could not resolve address 'docker-io-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:99] : 'server gcr.io' : could not resolve address 'gcr-io-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:105] : 'server ghcr.io' : could not resolve address 'ghcr-io-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:111] : 'server k8s.gcr.io' : could not resolve address 'k8s-gcr-io-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:117] : 'server icr.io' : could not resolve address 'icr-io-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:123] : 'server cp.icr.io' : could not resolve address 'cp-icr-io-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:129] : 'server nvcr.io' : could not resolve address 'nvcr-io-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:135] : 'server quay.io' : could not resolve address 'quay-io-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:141] : 'server registry.access.redhat.com' : could not resolve address 'redhat-access-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:147] : 'server registry.connect.redhat.com' : could not resolve address 'redhat-connect-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:153] : 'server registry.gitlab.com' : could not resolve address 'gitlab-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:159] : 'server registry.redhat.io' : could not resolve address 'redhat-io-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:165] : 'server registry.k8s.io' : could not resolve address 'k8s-io-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:171] : 'server registry.ci.openshift.org' : could not resolve address 'registry-ci-openshift-org-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:177] : 'server harbor.us.lmco.com' : could not resolve address 'harbor-us-nexus.'.", "[ALERT]    (115608) : parsing [/etc/haproxy/haproxy.cfg:183] : 'server harbor.global.lmco.com' : could not resolve address 'harbor-global-nexus.'.", "[ALERT]    (115608) : Failed to initialize server(s) addr."], "stdout": "", "stdout_lines": []}