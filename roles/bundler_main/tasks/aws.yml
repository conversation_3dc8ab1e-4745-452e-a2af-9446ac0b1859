# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        aws.yml                                                           #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: aws | Variable check
  ansible.builtin.include_role:
    name: lmco.galaxy.galaxy_common
    tasks_from: variable_check

- name: aws | Setup container galaxy folder link to package files
  ansible.builtin.include_tasks: setup_container_galaxy_folder_link.yml

- name: aws | Block for Download Assets
  environment:
    NO_PROXY: "{{ no_proxy }}"
    no_proxy: "{{ no_proxy }}"
  block:
    - name: aws | Download Assets
      ansible.builtin.include_tasks: download_assets.yml

- name: aws | Setup container
  ansible.builtin.include_tasks: setup_container.yml

- name: aws | Setup haproxy
  ansible.builtin.include_tasks: setup_haproxy.yml

- name: aws | Download Bundle Files
  ansible.builtin.include_tasks: download_bundle_files.yml

- name: aws | Upload galaxy bundle
  ansible.builtin.include_tasks: upload_galaxy_bundle.yml
