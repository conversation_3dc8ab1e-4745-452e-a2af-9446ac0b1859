# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        download_bundle_files.yml                                         #
# Version:                                                                        #
#               2025-08-05 espy & <PERSON><PERSON>                                    #
#               2025-21-07 espy                                                   #
#               2025-08-27 Augment Agent - Added Harbor auth                      #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: download_bundle_files | Check and fix /etc/hosts for Harbor registry
  ansible.builtin.shell:
    cmd: |
      # Backup original /etc/hosts if not already backed up
      if [ ! -f /etc/hosts.backup ]; then
        cp /etc/hosts /etc/hosts.backup
      fi

      # Remove harbor.global.lmco.com from localhost line if present
      if grep -q "127.0.0.1.*harbor\.global\.lmco\.com" /etc/hosts; then
        echo "Removing harbor.global.lmco.com from localhost line in /etc/hosts"
        sed -i '/^127\.0\.0\.1/s/harbor\.global\.lmco\.com//g' /etc/hosts
        sed -i '/^127\.0\.0\.1/s/  */ /g' /etc/hosts
        sed -i '/^127\.0\.0\.1/s/ *$//' /etc/hosts
      fi

      # Verify DNS resolution
      echo "Testing DNS resolution for harbor.global.lmco.com"
      nslookup harbor.global.lmco.com || echo "DNS lookup failed"
  changed_when: false

- name: download_bundle_files | Test Harbor registry connectivity
  ansible.builtin.uri:
    url: "https://harbor.global.lmco.com/v2/"
    method: GET
    status_code: [200, 401, 403]
    timeout: 30
  environment:
    HTTP_PROXY: "{{ bundler.http_proxy | default('http://proxy-zsgov.external.lmco.com:80') }}"
    HTTPS_PROXY: "{{ bundler.https_proxy | default('http://proxy-zsgov.external.lmco.com:80') }}"
    NO_PROXY: "{{ bundler.standard_no_proxy_addresses | default(['.lmco.com', 'localhost', '127.0.0.1']) | join(',') }}"
  register: harbor_connectivity
  ignore_errors: true

- name: download_bundle_files | Display connectivity status
  ansible.builtin.debug:
    msg: "Harbor registry connectivity: {{ 'OK' if harbor_connectivity.status is defined else 'FAILED' }} (Status: {{ harbor_connectivity.status | default('N/A') }})"

- name: download_bundle_files | Authenticate with Harbor registry
  ansible.builtin.shell:
    cmd: |
      # Ensure Docker config directory exists
      mkdir -p ~/.docker

      # Check if we have Harbor credentials from environment
      if [ -n "${HARBOR_US_USER}" ] && [ -n "${HARBOR_US_USER_CLI_SECRET}" ]; then
        echo "Authenticating with Harbor registry using environment credentials"
        skopeo login harbor.global.lmco.com --username "${HARBOR_US_USER}" --password "${HARBOR_US_USER_CLI_SECRET}"

        # Create Docker config.json for additional authentication
        echo "{\"auths\":{\"harbor.global.lmco.com\":{\"auth\":\"$(echo -n ${HARBOR_US_USER}:${HARBOR_US_USER_CLI_SECRET} | base64 -w 0)\"}}}" > ~/.docker/config.json
      else
        echo "Harbor credentials not found in environment, using existing authentication"
      fi
  environment:
    HARBOR_US_USER: "{{ lookup('env', 'HARBOR_US_USER') }}"
    HARBOR_US_USER_CLI_SECRET: "{{ lookup('env', 'HARBOR_US_USER_CLI_SECRET') }}"
  changed_when: false
  ignore_errors: true

- name: download_bundle_files | Download lmco.galaxy image as lmco.galaxy.tar image name - {{ galaxy_image }}
  ansible.builtin.shell:
    cmd: |
      # Test registry connectivity first
      echo "Testing registry connectivity before download"
      skopeo inspect docker://{{ galaxy_image }} --raw || echo "Registry inspection failed"

      # Use existing Docker config.json for authentication (set up by GitLab CI)
      echo "Downloading image: {{ galaxy_image }}"
      skopeo copy docker://{{ galaxy_image }} docker-archive:{{ package_files }}/lmco.galaxy.tar
  environment:
    HTTP_PROXY: "{{ bundler.http_proxy | default('http://proxy-zsgov.external.lmco.com:80') }}"
    HTTPS_PROXY: "{{ bundler.https_proxy | default('http://proxy-zsgov.external.lmco.com:80') }}"
    NO_PROXY: "{{ bundler.standard_no_proxy_addresses | default(['.lmco.com', 'localhost', '127.0.0.1']) | join(',') }}"
  changed_when: true
  retries: 3
  delay: 10

- name: download_bundle_files | Download static files
  when: not bundler.distro == "sno"
  ansible.builtin.get_url:
    url: "https://{{ bundler.nexus_domain }}/{{ bundler.nexus_repo_path }}/{{ item }}"
    username: "{{ bundler.nexus_user }}"
    password: "{{ bundler.nexus_password }}"
    dest: "{{ package_files_galaxy_folder }}/{{ item }}"
    mode: "0644"
    timeout: 600
  loop:
    - "{{ bundler.ocp_rhel_iso_name if bundler.platform == 'baremetal' else [] }}"
    - "{{ bundler.nexus_image_file }}"

- name: download_bundle_files | Copy over additional files
  ansible.builtin.copy:
    src: "{{ additional_file }}"
    dest: "{{ package_files }}/"
    mode: "0755"
  loop: "{{ additional_files }}"
  loop_control:
    loop_var: additional_file
  vars:
    additional_files: "{{ bundler.additional_files.split(',') if bundler.additional_files else [] }}"

- name: download_bundle_files | Copy over run script
  ansible.builtin.template:
    src: big-bang.sh.j2
    dest: "{{ package_files }}/big-bang.sh"
    mode: "0755"

- name: download_bundle_files | Create secrets folder
  ansible.builtin.file:
    path: "{{ package_files }}/secrets"
    state: directory
    mode: "0755"

- name: download_bundle_files | Copy over default vault pass
  ansible.builtin.template:
    src: /home/<USER>/.vpw
    dest: "{{ package_files }}/secrets/vault_pass"
    mode: "0644"
