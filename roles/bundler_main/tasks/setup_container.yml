# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        setup_container.yml                                               #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: setup_container | Create /etc/hosts copy
  ansible.builtin.copy:
    src: /etc/hosts
    dest: /tmp/hosts
    mode: "0644"

- name: setup_container | Replace a localhost entry searching for a literal string to avoid escaping
  ansible.builtin.lineinfile:
    path: /tmp/hosts
    regexp: '^127\.0\.0\.1'
    line: >-
      127.0.0.1 localhost localhost.localdomain localhost4 localhost4.localdomain4
      {{ bundler.default_registry_url if 'harbor.global.lmco.com' not in bundler.default_registry_url else '' }}
      {{ bundler.default_nexus_url }}
      {{ (
           domain_mappings
           | rejectattr('destination', 'search', '^harbor\\.')
           | map(attribute='destination')
           | join(' ')
         ) if proxying_mirror else "" }}

- name: setup_container | Update /etc/hosts
  ansible.builtin.shell:
    cmd: cat /tmp/hosts > /etc/hosts
  changed_when: true
  become: true
  become_user: root

- name: setup_container | Ensure Harbor registry is not in localhost line
  ansible.builtin.shell:
    cmd: |
      # Remove any harbor.global.lmco.com entries from localhost line
      if grep -q "127.0.0.1.*harbor\.global\.lmco\.com" /etc/hosts; then
        echo "Removing harbor.global.lmco.com from localhost line in /etc/hosts"
        sed -i '/^127\.0\.0\.1/s/harbor\.global\.lmco\.com//g' /etc/hosts
        sed -i '/^127\.0\.0\.1/s/  */ /g' /etc/hosts
        sed -i '/^127\.0\.0\.1/s/ *$//' /etc/hosts
      fi
  changed_when: false
  become: true
  become_user: root

- name: setup_container | Create directories
  ansible.builtin.file:
    path: "{{ item }}"
    mode: "0755"
    state: directory
  loop:
    - "/home/<USER>/.docker"
    - "{{ galaxy_nexus_repo_files_dir }}"
    - "{{ package_files_galaxy_folder }}"
    - "{{ apps_install_path }}/mirror-redhat2galaxy"
    - "/home/<USER>/.local/bin"
