# GitLab CI configuration for feature branch testing
# This file allows you to test your bundler role changes in the pipeline

stages:
  - build
  - test

variables:
  GIT_STRATEGY: clone
  ENABLE_SAST_SCANS: "false"
  GALAXY_VERSION: "latest"
  STORAGE_BACKEND: nexus
  CLUSTER_NAME: "feature-test"
  VERBOSITY: "-v"

# Include the feature branch deploy configuration
include:
  - local: 'feature-branch-deploy.yml'

# Alternative job that uses the current branch roles directly
test_feature_roles:
  image: harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:$GALAXY_VERSION
  stage: test
  timeout: 2h
  variables:
    ANSIBLE_FORCE_COLOR: "true"
    ADDITIONAL_FILES: "$CI_PROJECT_DIR/host_vars,$CI_PROJECT_DIR/group_vars,$CI_PROJECT_DIR/files,$CI_PROJECT_DIR/inventory.yml"
    OPENSHIFT_VARS: "$CI_PROJECT_DIR/group_vars/openshift.yml"
  rules:
    # Run on feature branches
    - if: $CI_COMMIT_REF_NAME != "main"
      when: manual
      allow_failure: true
    # Run when manually triggered
    - when: manual
      allow_failure: true
  before_script:
    - echo "Testing feature branch roles: $CI_COMMIT_REF_NAME"
    - echo "Current directory: $(pwd)"
    - echo "Available roles:"
    - ls -la
    
    # Set up authentication
    - mkdir -p ~/.docker
    - echo "Authenticating with Harbor registry"
    - skopeo login harbor.global.lmco.com --username ${HARBOR_US_USER} --password ${HARBOR_US_USER_CLI_SECRET} || echo "Login failed"
    
    # Install current branch roles to ansible collections path
    - mkdir -p ~/.ansible/collections/ansible_collections/lmco/
    - echo "Installing bundler roles from current branch"
    - cp -r . ~/.ansible/collections/ansible_collections/lmco/bundler/
    - ls -la ~/.ansible/collections/ansible_collections/lmco/bundler/
    
  script:
    - echo "Running syntax check on roles"
    - ansible-playbook --syntax-check ~/.ansible/collections/ansible_collections/lmco/bundler/bundler_main/tasks/main.yml || echo "Syntax check failed"
    
    # Test the download_bundle_files task specifically
    - echo "Testing download_bundle_files task"
    - |
      cat > test_download.yml << 'EOF'
      ---
      - hosts: localhost
        gather_facts: yes
        vars:
          bundler:
            http_proxy: "http://proxy-zsgov.external.lmco.com:80"
            https_proxy: "http://proxy-zsgov.external.lmco.com:80"
            standard_no_proxy_addresses:
              - .lmco.com
              - localhost
              - 127.0.0.1
            default_registry_url: "galaxy-registry.local"
            default_nexus_url: "galaxy-nexus.local"
          galaxy_image: "harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest"
          package_files: "/tmp/test_package_files"
        tasks:
          - name: Create test directory
            file:
              path: "{{ package_files }}"
              state: directory
              mode: '0755'
          
          - name: Test Harbor DNS resolution fix
            include_tasks: ~/.ansible/collections/ansible_collections/lmco/bundler/bundler_main/tasks/download_bundle_files.yml
      EOF
    
    - ansible-playbook test_download.yml -v || echo "Test playbook failed but continuing"
    
  artifacts:
    when: always
    expire_in: 1 day
    paths:
      - "*.log"
      - "test_download.yml"
  tags:
    - galaxy-xl
    - kubernetes
