#!/bin/bash

# Test script for feature branch bundler roles
# This script helps you test your changes locally before pushing to GitLab

set -e

echo "=== Feature Branch Bundler Roles Test ==="
echo "Testing Harbor registry DNS resolution fixes"
echo "Current branch: $(git branch --show-current 2>/dev/null || echo 'unknown')"
echo "Current directory: $(pwd)"
echo ""

# Check if we're in the right directory
if [ ! -d "bundler_main" ] || [ ! -d "bundler_common" ]; then
    echo "ERROR: This script should be run from the roles directory"
    echo "Expected to find bundler_main and bundler_common directories"
    exit 1
fi

# Create test environment
TEST_DIR="/tmp/bundler_test_$(date +%s)"
mkdir -p "$TEST_DIR"
echo "Created test directory: $TEST_DIR"

# Copy roles to test location
echo "Copying roles to test location..."
mkdir -p "$TEST_DIR/roles/lmco/bundler"
cp -r . "$TEST_DIR/roles/lmco/bundler/"

# Create a simple test playbook
cat > "$TEST_DIR/test_playbook.yml" << 'EOF'
---
- hosts: localhost
  gather_facts: yes
  vars:
    bundler:
      http_proxy: "http://proxy-zsgov.external.lmco.com:80"
      https_proxy: "http://proxy-zsgov.external.lmco.com:80"
      standard_no_proxy_addresses:
        - .lmco.com
        - localhost
        - 127.0.0.1
      default_registry_url: "harbor.global.lmco.com"  # This should NOT be added to localhost
      default_nexus_url: "galaxy-nexus.local"
    galaxy_image: "harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest"
    package_files: "/tmp/test_package_files"
    proxying_mirror: false
    domain_mappings: []
  
  tasks:
    - name: Create test directories
      file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - "{{ package_files }}"
        - "/tmp/test_package_files/galaxy_files"
    
    - name: Test setup_container task (should not add Harbor to localhost)
      include_tasks: roles/lmco/bundler/bundler_main/tasks/setup_container.yml
      
    - name: Check /etc/hosts for Harbor registry
      shell: |
        echo "=== Current /etc/hosts content ==="
        cat /etc/hosts
        echo ""
        echo "=== Checking for Harbor in localhost line ==="
        if grep -q "127.0.0.1.*harbor\.global\.lmco\.com" /etc/hosts; then
          echo "ERROR: Harbor registry found in localhost line!"
          exit 1
        else
          echo "SUCCESS: Harbor registry not in localhost line"
        fi
      register: hosts_check
      
    - name: Display hosts check result
      debug:
        var: hosts_check.stdout_lines
        
    - name: Test DNS resolution fix task
      include_tasks: roles/lmco/bundler/bundler_main/tasks/download_bundle_files.yml
      vars:
        # Skip the actual download for testing
        skip_download: true
EOF

# Create ansible.cfg for the test
cat > "$TEST_DIR/ansible.cfg" << 'EOF'
[defaults]
host_key_checking = False
stdout_callback = yaml
roles_path = ./roles
collections_paths = ./collections
EOF

echo ""
echo "=== Running Ansible syntax check ==="
cd "$TEST_DIR"
ansible-playbook --syntax-check test_playbook.yml

echo ""
echo "=== Running test playbook (dry run) ==="
ansible-playbook test_playbook.yml --check -v

echo ""
echo "=== Test Summary ==="
echo "✓ Syntax check passed"
echo "✓ Dry run completed"
echo "✓ Harbor registry DNS fixes are in place"
echo ""
echo "Test directory: $TEST_DIR"
echo "You can manually run: cd $TEST_DIR && ansible-playbook test_playbook.yml"
echo ""
echo "=== Next Steps ==="
echo "1. Commit your changes to your feature branch"
echo "2. Push the branch to GitLab"
echo "3. Use the .gitlab-ci.yml file to run the pipeline"
echo "4. Or manually trigger the 'test_feature_roles' job in GitLab"
