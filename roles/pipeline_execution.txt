[0KRunning with gitlab-runner 18.2.0 (c24769e8)[0;m
[0K  on galaxy-xl-gitlab.global.lmco.com-runner-7c54cb7d8b-vngvc kzT5Roj16, system ID: r_bhFJN4s6JJhi[0;m
[0K  feature flags: FF_USE_ADVANCED_POD_SPEC_CONFIGURATION:true[0;m
[0K[36;1mResolving secrets[0;m[0;m
section_start:1756104739:prepare_executor
[0K[0K[36;1mPreparing the "kubernetes" executor[0;m[0;m
[0KUsing Kubernetes namespace: gitlab[0;m
[0KUsing Kubernetes executor with image harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest ...[0;m
[0KUsing attach strategy to execute scripts...[0;m
[0KUsing effective pull policy of [] for container build[0;m
[0KUsing effective pull policy of [] for container helper[0;m
[0KUsing effective pull policy of [] for container init-permissions[0;m
section_end:1756104739:prepare_executor
[0Ksection_start:1756104739:prepare_script
[0K[0K[36;1mPreparing environment[0;m[0;m
[0KUsing FF_USE_POD_ACTIVE_DEADLINE_SECONDS, the Pod activeDeadlineSeconds will be set to the job timeout: 12h0m0s...[0;m
[0;33mWARNING: Advanced Pod Spec configuration enabled, merging the provided PodSpec to the generated one. This is a beta feature and is subject to change. Feedback is collected in this issue: https://gitlab.com/gitlab-org/gitlab-runner/-/issues/29659 ...[0;m
Waiting for pod gitlab/runner-kzt5roj16-project-13816-concurrent-0-xlb3upex to be running, status is Pending
Waiting for pod gitlab/runner-kzt5roj16-project-13816-concurrent-0-xlb3upex to be running, status is Pending
	Unschedulable: "0/17 nodes are available: waiting for ephemeral volume controller to create the persistentvolumeclaim \"runner-kzt5roj16-project-13816-concurrent-0-xlb3upex-containers\". preemption: 0/17 nodes are available: 17 Preemption is not helpful for scheduling."
Waiting for pod gitlab/runner-kzt5roj16-project-13816-concurrent-0-xlb3upex to be running, status is Pending
	Unschedulable: "0/17 nodes are available: waiting for ephemeral volume controller to create the persistentvolumeclaim \"runner-kzt5roj16-project-13816-concurrent-0-xlb3upex-containers\". preemption: 0/17 nodes are available: 17 Preemption is not helpful for scheduling."
Waiting for pod gitlab/runner-kzt5roj16-project-13816-concurrent-0-xlb3upex to be running, status is Pending
	ContainersNotInitialized: "containers with incomplete status: [init-permissions]"
	ContainersNotReady: "containers with unready status: [build helper]"
	ContainersNotReady: "containers with unready status: [build helper]"
Waiting for pod gitlab/runner-kzt5roj16-project-13816-concurrent-0-xlb3upex to be running, status is Pending
	ContainersNotInitialized: "containers with incomplete status: [init-permissions]"
	ContainersNotReady: "containers with unready status: [build helper]"
	ContainersNotReady: "containers with unready status: [build helper]"
Waiting for pod gitlab/runner-kzt5roj16-project-13816-concurrent-0-xlb3upex to be running, status is Pending
	ContainersNotInitialized: "containers with incomplete status: [init-permissions]"
	ContainersNotReady: "containers with unready status: [build helper]"
	ContainersNotReady: "containers with unready status: [build helper]"
Waiting for pod gitlab/runner-kzt5roj16-project-13816-concurrent-0-xlb3upex to be running, status is Pending
	ContainersNotInitialized: "containers with incomplete status: [init-permissions]"
	ContainersNotReady: "containers with unready status: [build helper]"
	ContainersNotReady: "containers with unready status: [build helper]"
Waiting for pod gitlab/runner-kzt5roj16-project-13816-concurrent-0-xlb3upex to be running, status is Pending
	ContainersNotInitialized: "containers with incomplete status: [init-permissions]"
	ContainersNotReady: "containers with unready status: [build helper]"
	ContainersNotReady: "containers with unready status: [build helper]"
Running on runner-kzt5roj16-project-13816-concurrent-0-xlb3upex via galaxy-xl-gitlab.global.lmco.com-runner-7c54cb7d8b-vngvc...

section_end:1756104764:prepare_script
[0Ksection_start:1756104764:get_sources
[0K[0K[36;1mGetting source from Git repository[0;m[0;m
[32;1mGitaly correlation ID: 01K3FZZHNX3H8ZH3GKSC2V5E91[0;m
[32;1mFetching changes with git depth set to 20...[0;m
Initialized empty Git repository in /builds/galaxy/program/testbeds/andromeda/.git/
[32;1mCreated fresh repository.[0;m
[32;1mChecking out d161c07b as detached HEAD (ref is refs/merge-requests/1/head)...[0;m

[32;1mSkipping Git submodules setup[0;m

section_end:1756104765:get_sources
[0Ksection_start:1756104765:step_script
[0K[0K[36;1mExecuting "step_script" stage of the job script[0;m[0;m
[32;1m$ welcome[0;m
[32m
                                                                                  *       +
  _______      ___       __          ___      ___   ___ ____    ____        `                  |
 /  _____|    /   \     |  |        /   \     \  \ /  / \   \  /   /    ()    .-.,="``"=.    - o -
|  |  __     /  ^  \    |  |       /  ^  \     \  V  /   \   \/   /           `=/_       \     |
|  | |_ |   /  /_\  \   |  |      /  /_\  \     >   <     \_    _/         *   |  `=._    |
|  |__| |  /  _____  \  |  `----./  _____  \   /  .  \      |  |                \     `=./`,        `
 \______| /__/     \__\ |_______/__/     \__\ /__/ \__\     |__|             .   `=.__.=` `=`      *
                                                                    +                         +
                                                                         O      *        `       .
[0m
[36mLinux Base Image: Red Hat Enterprise Linux release 9.5 (Plow)[0m
[32mPython: Python 3.12.1[0m
[91mAnsible: 9.9.0[0m
[91mAnsible Core: 2.16.14[0m
[93mAnsible Environment Build Date: 2025-07-21 18:11:14[0m
[94mExecution Environment Build Date: 2025-07-21 18:14:26[0m
[94mGalaxy Version: 3.3.39 [0m
[90mGalaxy Environment Build Date: 2025-08-22 11:02:40[0m
[32;1m$ env[0;m
KUBERNETES_SERVICE_PORT_HTTPS=443
CI_PROJECT_NAMESPACE=galaxy/program/testbeds
GITLAB_USER_ID=18774
RED_HAT_ACCOUNT_TOKEN=eyJhbGciOiJIUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI0NzQzYTkzMC03YmJiLTRkZGQtOTgzMS00ODcxNGRlZDc0YjUifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EO5L76HQE412wjgcEoZwS7bqhAMnJiFMcaGAnUU69a98LUKl1oZ-s98S7xeTo_78KHPW4D67HMvJDHF8gl_DOQ
PROJECT_PROTECTED_BRANCH_USERS=merge_access_level=40&unprotect_access_level=40&push_access_level=0&allowed_to_push%5B%5D%5Buser_id%5D=16925

CI_RUNNER_VERSION=18.2.0
AWS_S3_ENDPOINT_URL_GOV_WEST=https://s3-fips.us-gov-west-1.amazonaws.com
CI_MERGE_REQUEST_TARGET_BRANCH_PROTECTED=true
AWS_S3_ENDPOINT_URL=https://s3-fips.us-gov-east-1.amazonaws.com
FF_SKIP_NOOP_BUILD_STAGES=true
KUBERNETES_SERVICE_PORT=443
AWS_DEFAULT_REGION_GOV_WEST=us-gov-west-1
FF_USE_INIT_WITH_DOCKER_EXECUTOR=false
CI_SERVER_NAME=GitLab
CI_RUNNER_DESCRIPTION=galaxy-xl-gitlab.global.lmco.com-runner-7c54cb7d8b-vngvc
GITLAB_USER_EMAIL=<EMAIL>
SC_TOKEN_DEV=scLocalGitPwd2022!
CI_SERVER_REVISION=5d5acd0e176
CI_MERGE_REQUEST_SOURCE_BRANCH_PROTECTED=false
FF_USE_WINDOWS_LEGACY_PROCESS_STRATEGY=false
CI_MERGE_REQUEST_SOURCE_BRANCH_NAME=XBA_GALAXY_INF-386/ocp_install
CI_MERGE_REQUEST_TARGET_BRANCH_SHA=
CI_PIPELINE_NAME=
CI_RUNNER_EXECUTABLE_ARCH=linux/amd64
CI_REGISTRY_USER=gitlab-ci-token
CLUSTER_NAME=andromeda
CI_REGISTRY_PASSWORD=[MASKED]
CI_API_V4_URL=https://gitlab.global.lmco.com/api/v4
CI_RUNNER_SHORT_TOKEN=kzT5Roj16
ASAP_TOKEN=[MASKED]
CI_JOB_NAME=generate_galaxy_bundle
CI_OPEN_MERGE_REQUESTS=galaxy/program/testbeds/andromeda!1
CP1_CICD_GALAXY_DEPLOYER_HOST=api.ocp-ugw1-cp1.ecs.us.lmco.com:6443
HOSTNAME=runner-kzt5roj16-project-13816-concurrent-0-xlb3upex
K8S_AUTH_KUBECONFIG=~/.kube/config
PUBLISH_IMAGE=false
GITLAB_USER_LOGIN=n6845g
TEST_OPERATOR=false
CI_PROJECT_NAME=andromeda
CI_PIPELINE_SOURCE=merge_request_event
NSS_SDB_USE_CACHE=no
GALAXYEE_VER=latest
REDHAT_PULL_SECRET={"auths":{"cloud.openshift.com":{"auth":"********************************************************************************************************************************************************************************","email":"[MASKED].<EMAIL>"},"quay.io":{"auth":"********************************************************************************************************************************************************************************","email":"[MASKED].<EMAIL>"},"cp.icr.io":{"auth":"************************************************************************************************************************************************************************************************************************************************"},"registry.connect.redhat.com":{"auth":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","email":"[MASKED].<EMAIL>"},"registry.redhat.io":{"auth":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","email":"[MASKED].<EMAIL>"}}}
HARBOR_US_USER_CLI_SECRET=[MASKED]
CI_JOB_STATUS=running
NODE_EXTRA_CA_CERTS=/etc/pki/ca-trust/source/anchors/Combined_pem.pem
CI_PIPELINE_ID=6670440
FF_DISABLE_POWERSHELL_STDIN=false
CI_COMMIT_REF_SLUG=xba-galaxy-inf-386-ocp-install
CI_MERGE_REQUEST_SOURCE_PROJECT_PATH=galaxy/program/testbeds/andromeda
CI_SERVER=yes
RED_HAT_ACCOUNT_EMAIL=[MASKED].<EMAIL>
COLLECTION_PREFIX_NAME=andromeda
AWS_DEFAULT_REGION=us-gov-east-1
CICD_GALAXY_DEPLOYER_TOKEN=*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FF_SET_PERMISSIONS_BEFORE_CLEANUP=true
PROJECT_PROTECTED_BRANCH_REGEX=^(main|(|feature/|(|hot)fix/|bug/)(XBA_GALAXY_(AUTO|ASST|SUST|INF|INT|KUB|CP4D))-\d+)($|/+.+)
COLLECTION_TAG=
AWS_SECRET_ACCESS_KEY_GOV_WEST=[MASKED]
REGISTRY=harbor.global.lmco.com/lmc.space.galaxy
SEMANTIC_RELEASE_IMAGE=harbor.global.lmco.com/lmc.eo.swf.pipelines/core/semantic-release
CI_COMMIT_SHORT_SHA=d161c07b
CI_JOB_NAME_SLUG=generate-galaxy-bundle
RUNNER_TEMP_PROJECT_DIR=/builds/galaxy/program/testbeds/andromeda.tmp
FF_USE_GIT_BUNDLE_URIS=true
CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX=gitlab.global.lmco.com:443/galaxy/dependency_proxy/containers
COLLECTION_NS=
GALAXY_SSH_KEY=/builds/galaxy/program/testbeds/andromeda.tmp/GALAXY_SSH_KEY
ENABLE_SEMANTIC_RELEASE=true
CONFLUENCE_PAGE_ID=98378
ENABLE_EFOSS_REQUEST=true
PWD=/builds/galaxy/program/testbeds/andromeda
SC_USERNAME=local_gitlab_change
CI_RUNNER_TAGS=["galaxy-xl", "kubernetes", "aws", "docker-builder"]
CI_PROJECT_PATH=galaxy/program/testbeds/andromeda
CI_MERGE_REQUEST_DIFF_BASE_SHA=ac21574d53d2489e06f659dad999b8efa09e5442
CI_MERGE_REQUEST_SOURCE_PROJECT_URL=https://gitlab.global.lmco.com/galaxy/program/testbeds/andromeda
FF_TIMESTAMPS=false
FF_USE_NEW_BASH_EVAL_STRATEGY=false
FF_MASK_ALL_DEFAULT_TOKENS=true
AWS_S3_BUCKET_GOV_WEST=lm-galaxy
CI_SERVER_TLS_CA_FILE=/builds/galaxy/program/testbeds/andromeda.tmp/CI_SERVER_TLS_CA_FILE
CI_DEPENDENCY_PROXY_DIRECT_GROUP_IMAGE_PREFIX=gitlab.global.lmco.com:443/galaxy/program/testbeds/dependency_proxy/containers
GALAXY_VERSION=latest
CICD_GALAXY_DEPLOYER_DOMAIN=venus.ssc.lmco.com
CI_MERGE_REQUEST_PROJECT_URL=https://gitlab.global.lmco.com/galaxy/program/testbeds/andromeda
FF_USE_LEGACY_S3_CACHE_ADAPTER=false
FF_WAIT_FOR_POD_TO_BE_REACHABLE=false
GIT_COMMITTER_NAME=galaxy-bot
CI_MERGE_REQUEST_ASSIGNEES=n6845g
COLLECTION_EXTRA_VARS=
GIT_STRATEGY=clone
FF_DISABLE_UMASK_FOR_KUBERNETES_EXECUTOR=false
CI_COMMIT_REF_PROTECTED=false
AAP_IMAGE_NAME=andromeda-aap
NEXUS_PASSWORD=[MASKED]
container=oci
FF_USE_POWERSHELL_PATH_RESOLVER=false
NEXUS_URL=galaxy-nexus.apps.galaxy-uge1-ocp02.us.lmco.com
CI_MERGE_REQUEST_TITLE=fix: Updated valeus with pub account
FF_USE_DOCKER_AUTOSCALER_DIAL_STDIO=true
SAST_SCAN=sonarqube
CP1_CICD_GALAXY_DEPLOYER_DOMAIN=apps.ocp-ugw1-cp1.ecs.us.lmco.com
CI_API_GRAPHQL_URL=https://gitlab.global.lmco.com/api/graphql
********************
FF_USE_NATIVE_STEPS=true
CI_SERVER_VERSION_MINOR=7
HOME=/home/<USER>
CI_COMMIT_SHA=d161c07bc3b91b1228709c8169a90abb5b6635ca
EFOSS_USER=[MASKED]
FF_NETWORK_PER_BUILD=false
CI_DEPENDENCY_PROXY_PASSWORD=[MASKED]
NEXUS_USER=galaxy-hivestar
HTTP_PORT=8090
KUBERNETES_PORT_443_TCP=tcp://**********:443
CI_JOB_TIMEOUT=43200
CI_PROJECT_VISIBILITY=internal
CI_CONCURRENT_PROJECT_ID=0
FF_SCRIPT_SECTIONS=false
SC_TOKEN=[MASKED]
CP1_CICD_GALAXY_DEPLOYER_CLUSTER=ocp-ugw1-cp1.ecs.us
CI_COMMIT_MESSAGE=fixing gitlab ci file

FF_USE_WINDOWS_JOB_OBJECT=false
CONFLUENCE_SPACE_KEY=GAL
CI_SERVER_SHELL_SSH_PORT=22
CI_JOB_JWT_V1=[MASKED]
CI_JOB_JWT_V2=[MASKED]
SAST_IMAGE=harbor-global-nexus.apps.galaxy-uge1-ocp02.us.lmco.com/lmc.eo.asap/lmco-security/static-code-scanner:latest
ANSIBLE_FORCE_COLOR=true
FF_DISABLE_AUTOMATIC_TOKEN_ROTATION=false
VALIDATE_WARN_J2=true
FF_USE_DIRECT_DOWNLOAD=true
CI_PAGES_DOMAIN=pages.gitlab.global.lmco.com
FF_PRINT_POD_EVENTS=false
CI_SERVER_VERSION=16.7.10-ee
CI_MERGE_REQUEST_PROJECT_PATH=galaxy/program/testbeds/andromeda
FF_USE_POD_ACTIVE_DEADLINE_SECONDS=true
GLOBAL_NEXUS_PASSWORD=[MASKED]
CI_REGISTRY=registry.gitlab.global.lmco.com:443
CI_SERVER_PORT=443
_CONTAINERS_USERNS_CONFIGURED=
CI_MERGE_REQUEST_IID=1
STORAGE_BACKEND=nexus
AWS_SECRET_ACCESS_KEY=54/igUMnH87QLhNJOFrrldP3BxRQjwO1Aw9+FO4y
CI_PROJECT_NAMESPACE_ID=32749
OPENSHIFT_VARS=/builds/galaxy/program/testbeds/andromeda/group_vars/openshift.yml
BUILDER_USER=galaxy
FF_TEST_FEATURE=false
CI_MERGE_REQUEST_DESCRIPTION=Closes XBA_GALAXY_INF-386
PRE_REMOVE_COLLECTION=false
IMAGE_NAME=andromeda
RED_HAT_ACCOUNT_PASSWORD=Df^Yr37NxwhTCUY
CI_MERGE_REQUEST_PROJECT_ID=13816
CI_PAGES_URL=https://galaxy.pages.gitlab.global.lmco.com/program/testbeds/andromeda
CI_MERGE_REQUEST_ID=220723
CI_PIPELINE_IID=17
GIT_AUTHOR_EMAIL=<EMAIL>
CI_REPOSITORY_URL=https://gitlab-ci-token:[MASKED]@gitlab.global.lmco.com/galaxy/program/testbeds/andromeda.git
FF_USE_FLEETING_ACQUIRE_HEARTBEATS=false
CI_SERVER_URL=https://gitlab.global.lmco.com
NEXUS_US_USER=[MASKED]
FF_ENABLE_BASH_EXIT_CODE_CHECK=false
GITLAB_FEATURES=audit_events,blocked_issues,board_iteration_lists,code_owners,code_review_analytics,full_codequality_report,group_activity_analytics,group_bulk_edit,issuable_default_templates,issue_weights,iterations,ldap_group_sync,merge_request_approvers,milestone_charts,multiple_issue_assignees,multiple_ldap_servers,multiple_merge_request_assignees,multiple_merge_request_reviewers,project_merge_request_analytics,protected_refs_for_users,push_rules,resource_access_token,seat_link,usage_quotas,visual_review_app,wip_limits,zoekt_code_search,blocked_work_items,description_diffs,send_emails_from_admin_area,repository_size_limit,maintenance_mode,scoped_issue_board,contribution_analytics,group_webhooks,member_lock,elastic_search,repository_mirrors,adjourned_deletion_for_projects_and_groups,admin_audit_log,auditor_user,blocking_merge_requests,board_assignee_lists,board_milestone_lists,ci_namespace_catalog,ci_secrets_management,ci_pipeline_cancellation_restrictions,cluster_agents_ci_impersonation,cluster_agents_user_impersonation,cluster_deployments,code_owner_approval_required,code_suggestions,commit_committer_check,commit_committer_name_check,compliance_framework,custom_compliance_frameworks,custom_file_templates,custom_project_templates,cycle_analytics_for_groups,cycle_analytics_for_projects,db_load_balancing,default_branch_protection_restriction_in_groups,default_project_deletion_protection,delete_unconfirmed_users,dependency_proxy_for_packages,disable_name_update_for_users,disable_personal_access_tokens,domain_verification,epics,extended_audit_events,external_authorization_service_api_management,feature_flags_code_references,file_locks,geo,generic_alert_fingerprinting,git_two_factor_enforcement,group_allowed_email_domains,group_coverage_reports,group_forking_protection,group_milestone_project_releases,group_project_templates,group_repository_analytics,group_saml,group_scoped_ci_variables,ide_schema_config,incident_metric_upload,instance_level_scim,jira_issues_integration,ldap_group_sync_filter,merge_request_performance_metrics,admin_merge_request_approvers_rules,merge_trains,metrics_reports,metrics_observability,multiple_alert_http_integrations,multiple_approval_rules,multiple_group_issue_boards,object_storage,microsoft_group_sync,operations_dashboard,package_forwarding,pages_size_limit,pages_multiple_versions,productivity_analytics,project_aliases,protected_environments,reject_non_dco_commits,reject_unsigned_commits,remote_development,saml_group_sync,service_accounts,scoped_labels,smartcard_auth,ssh_certificates,swimlanes,target_branch_rules,type_of_work_analytics,minimal_access_role,unprotection_restrictions,ci_project_subscriptions,incident_timeline_view,oncall_schedules,escalation_policies,zentao_issues_integration,coverage_check_approval_rule,issuable_resource_links,group_protected_branches,group_level_merge_checks_setting,oidc_client_groups_claim,disable_deleting_account_for_users,disable_private_profiles,group_ip_restriction,issues_analytics,password_complexity,group_wikis,email_additional_text,custom_file_templates_for_namespace,incident_sla,export_user_permissions,cross_project_pipelines,feature_flags_related_issues,merge_pipelines,ci_cd_projects,github_integration,ai_chat,ai_config_chat,ai_features,ai_git_command,ai_tanuki_bot,ai_analyze_ci_job_failure,api_discovery,api_fuzzing,auto_rollback,breach_and_attack_simulation,fill_in_merge_request_template,cluster_image_scanning,external_status_checks,combined_project_analytics_dashboards,compliance_pipeline_configuration,container_scanning,credentials_inventory,custom_roles,dast,dependency_scanning,dora4_analytics,enterprise_templates,environment_alerts,evaluate_group_level_compliance_pipeline,explain_code,external_audit_events,experimental_features,generate_description,generate_commit_message,generate_test_file,git_abuse_rate_limit,group_ci_cd_analytics,group_level_compliance_dashboard,group_level_analytics_dashboard,incident_management,inline_codequality,insights,issuable_health_status,issues_completed_analytics,jira_vulnerabilities_integration,jira_issue_association_enforcement,kubernetes_cluster_vulnerabilities,license_scanning,okrs,personal_access_token_expiration_policy,pre_receive_secret_detection,product_analytics,project_quality_summary,project_level_analytics_dashboard,prometheus_alerts,quality_management,related_epics,release_evidence_test_artifacts,report_approver_rules,required_ci_templates,requirements,runner_maintenance_note,runner_performance_insights,runner_upgrade_management,runner_upgrade_management_for_namespace,sast,sast_iac,sast_custom_rulesets,sast_fp_reduction,secret_detection,security_configuration_in_ui,security_dashboard,security_on_demand_scans,security_orchestration_policies,security_training,ssh_key_expiration_policy,summarize_mr_changes,summarize_my_mr_code_review,summarize_notes,summarize_submitted_review,stale_runner_cleanup_for_namespace,status_page,suggested_reviewers,subepics,tracing,unique_project_download_limit,vulnerability_auto_fix,vulnerability_finding_signatures,coverage_fuzzing,devops_adoption,group_level_devops_adoption,instance_level_devops_adoption
VAULT_PW=[MASKED]
CONFLUENCE_URL=https://atlassian-confluence-confluence.apps.galaxy-uge1-ocp02.us.lmco.com
FF_USE_GITALY_CORRELATION_ID=true
CI_MERGE_REQUEST_REF_PATH=refs/merge-requests/1/head
CI_COMMIT_DESCRIPTION=
FF_USE_ADVANCED_POD_SPEC_CONFIGURATION=true
GIT_CREDENTIALS=gitlab-ci-token:[MASKED]
CI_TEMPLATE_REGISTRY_HOST=registry.gitlab.com
CI_JOB_STAGE=build
CI_MERGE_REQUEST_DIFF_ID=1355265
CI_PIPELINE_URL=https://gitlab.global.lmco.com/galaxy/program/testbeds/andromeda/-/pipelines/6670440
FF_EXPORT_HIGH_CARDINALITY_METRICS=false
CI_DEFAULT_BRANCH=main
JIRA_PAT=Njg3NDk1NzQ4ODE0OrK6i2jKFcT02ZmzL1vMauPwwMgR
FF_GIT_URLS_WITHOUT_TOKENS=false
GIT_COMMITTER_EMAIL=<EMAIL>
CP1_CICD_GALAXY_DEPLOYER_TOKEN=[MASKED]
CI_MERGE_REQUEST_TARGET_BRANCH_NAME=main
CI_MERGE_REQUEST_SOURCE_BRANCH_SHA=
GITLAB_ENV=/builds/galaxy/program/testbeds/andromeda.tmp/gitlab_runner_env
CI_MERGE_REQUEST_SQUASH_ON_MERGE=false
TERM=xterm
AWS_S3_BUCKET=lm-galaxy-uge1
CI_SERVER_VERSION_PATCH=10
VERSION_TAG=XBA_GALAXY_INF-386
CI_COMMIT_TITLE=fixing gitlab ci file
REGISTRY_USER=[MASKED]
KEEP_COLLECTION=false
CICD_GALAXY_DEPLOYER_HOST=https://api.venus.ssc.lmco.com:6443
CI_PROJECT_ROOT_NAMESPACE=galaxy
FF_ENABLE_JOB_CLEANUP=false
FF_RESOLVE_FULL_TLS_CHAIN=false
CI_MERGE_REQUEST_SOURCE_PROJECT_ID=13816
GITLAB_USER_NAME=KHIZAR, AGAH
IMAGE_TAG=XBA_GALAXY_INF-386
CI_PROJECT_DIR=/builds/galaxy/program/testbeds/andromeda
CONFLUENCE_PAT=MjczMjM0NTc0ODUzOmijo8qFFkb+ePvH/EXplznRNA0y
CI_MERGE_REQUEST_EVENT_TYPE=detached
SHLVL=3
ENTERPRISE_OCP_PASSWORD=[MASKED]
CICD_GALAXY_DEPLOYER_CLUSTER=venus.ssc
CI_RUNNER_ID=5633
NEXUS_US_PASSWORD=[MASKED]
CI_PIPELINE_CREATED_AT=2025-08-25T06:51:08Z
CI_COMMIT_TIMESTAMP=2025-08-25T02:50:56-04:00
AWS_ACCESS_KEY_ID=AKIAWUXEJXEPXGIS2RFJ
CI_DISPOSABLE_ENVIRONMENT=true
CI_SERVER_SHELL_SSH_HOST=gitlab.global.lmco.com
CI_JOB_JWT=[MASKED]
CI_REGISTRY_IMAGE=registry.gitlab.global.lmco.com:443/galaxy/program/testbeds/andromeda
KUBERNETES_PORT_443_TCP_PROTO=tcp
ENTERPRISE_OCP_USERNAME=[MASKED]
CI_SERVER_PROTOCOL=https
CI_MERGE_REQUEST_APPROVED=true
KUBERNETES_PORT_443_TCP_ADDR=**********
CI_COMMIT_AUTHOR=Cloud User <<EMAIL>>
FF_POSIXLY_CORRECT_ESCAPES=false
COLLECTION_NAME=andromeda
CI_COMMIT_REF_NAME=XBA_GALAXY_INF-386/ocp_install
CI_SERVER_HOST=gitlab.global.lmco.com
FF_USE_DUMB_INIT_WITH_KUBERNETES_EXECUTOR=false
CI_JOB_URL=https://gitlab.global.lmco.com/galaxy/program/testbeds/andromeda/-/jobs/33291796
CI_JOB_TOKEN=[MASKED]
VERBOSITY=
CI_CONCURRENT_ID=1
CI_JOB_STARTED_AT=2025-08-25T06:52:18Z
CI_PROJECT_DESCRIPTION=
FF_USE_LEGACY_KUBERNETES_EXECUTION_STRATEGY=false
CI_PROJECT_CLASSIFICATION_LABEL=
CI_RUNNER_REVISION=c24769e8
GLOBAL_NEXUS_USER=[MASKED]
FF_KUBERNETES_HONOR_ENTRYPOINT=false
FF_CLEAN_UP_FAILED_CACHE_EXTRACT=false
TEST_COLLECTION=false
CI_DEPENDENCY_PROXY_USER=gitlab-ci-token
REQUESTS_CA_BUNDLE=/etc/pki/ca-trust/source/anchors/Combined_pem.pem
CI_JOB_MANUAL=true
FF_USE_DYNAMIC_TRACE_FORCE_SEND_INTERVAL=false
FF_DISABLE_UMASK_FOR_DOCKER_EXECUTOR=false
CI_PROJECT_PATH_SLUG=galaxy-program-testbeds-andromeda
CI_NODE_TOTAL=1
KUBERNETES_SERVICE_HOST=**********
FF_USE_GIT_NATIVE_CLONE=false
KUBERNETES_PORT=tcp://**********:443
RESOURCE_GROUP=NULL_GROUP
KUBERNETES_PORT_443_TCP_PORT=443
BUILDER_LMI_IP=*************
TEST_WARN=true
FF_USE_ADAPTIVE_REQUEST_CONCURRENCY=true
CI_BUILDS_DIR=/builds
CI_JOB_ID=33291796
GALAXY_FOLDER=/home/<USER>/deployments/andromeda
CI_PROJECT_REPOSITORY_LANGUAGES=
GALAXY_IMAGE=harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy
AWS_CA_BUNDLE=/etc/pki/ca-trust/source/anchors/Combined_pem.pem
HARBOR_US_USER=robot_lmc.space.mbea.ivs+galaxy-il-pull-robo
GIT_AUTHOR_NAME=galaxy-bot
TEST_K8S=false
FF_LOG_IMAGES_CONFIGURED_FOR_JOB=false
PATH=/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
FF_SECRET_RESOLVING_FAILS_IF_MISSING=true
CI_PROJECT_ID=13816
CI=true
GITLAB_CI=true
GITLAB_TOKEN=[MASKED]
CI_JOB_IMAGE=harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest
ENTERPRISE_OCP_DEPLOYER_TOKEN=[MASKED]
CI_PROJECT_TITLE=Andromeda
CI_COMMIT_BEFORE_SHA=0000000000000000000000000000000000000000
CI_SERVER_VERSION_MAJOR=16
FF_USE_EXPONENTIAL_BACKOFF_STAGE_RETRY=true
CI_CONFIG_PATH=.gitlab-ci.yml
BUILDER_MGMT_IP=*************
REGISTRY_PASSWORD=[MASKED]
FF_USE_LEGACY_GCS_CACHE_ADAPTER=false
FF_USE_FASTZIP=false
CI_DEPENDENCY_PROXY_SERVER=gitlab.global.lmco.com:443
CI_PROJECT_URL=https://gitlab.global.lmco.com/galaxy/program/testbeds/andromeda
COLLECTION_ROLE=
ENABLE_SAST_SCANS=false
EFOSS_TOKEN=[MASKED]
AWS_ACCESS_KEY_ID_GOV_WEST=[MASKED]
ADDITIONAL_FILES=/builds/galaxy/program/testbeds/andromeda/host_vars,/builds/galaxy/program/testbeds/andromeda/group_vars,/builds/galaxy/program/testbeds/andromeda/files,/builds/galaxy/program/testbeds/andromeda/inventory.yml
_=/usr/bin/env
OLDPWD=/home/<USER>
[32;1m$ ansible-playbook -i localhost lmco.bundler.create_bundle.yml -e "@$OPENSHIFT_VARS" -e "{'cli_galaxy':{'version':'${GALAXY_VERSION}'}}" -e "{'bundle_name':'${CLUSTER_NAME}' }" -e "{'cli_bundler':{'additional_files':'${ADDITIONAL_FILES}'}}" $VERBOSITY[0;m
[1;35m[WARNING]: Unable to parse /builds/galaxy/program/testbeds/andromeda/localhost[0m
[1;35mas an inventory source[0m
[1;35m[WARNING]: No inventory was parsed, only implicit localhost is available[0m
[1;35m[WARNING]: provided hosts list is empty, only localhost is available. Note that[0m
[1;35mthe implicit localhost does not match 'all'[0m

PLAY [Run tasks to create standard files] **************************************

TASK [Gathering Facts] *********************************************************
Monday 25 August 2025  06:52:46 +0000 (0:00:00.063)       0:00:00.063 ********* 
[0;32mok: [localhost][0m

TASK [Common bundler] **********************************************************
Monday 25 August 2025  06:52:47 +0000 (0:00:00.869)       0:00:00.933 ********* 

TASK [lmco.galaxy.galaxy_common : Validating arguments against arg spec 'main' - Galaxy Common Role] ***
Monday 25 August 2025  06:52:47 +0000 (0:00:00.035)       0:00:00.968 ********* 
[0;32mok: [localhost][0m

TASK [lmco.galaxy.galaxy_common : Generate control_dict] ***********************
Monday 25 August 2025  06:52:47 +0000 (0:00:00.054)       0:00:01.022 ********* 
[0;32mok: [localhost][0m

TASK [lmco.galaxy.galaxy_common : Display Galaxy Inputs] ***********************
Monday 25 August 2025  06:52:47 +0000 (0:00:00.037)       0:00:01.059 ********* 
[0;36mskipping: [localhost] => (item=default_galaxy) [0m
[0;36mskipping: [localhost] => (item=lmco_galaxy) [0m
[0;36mskipping: [localhost] => (item=cli_galaxy) [0m
[0;36mskipping: [localhost] => (item=galaxy) [0m
[0;36mskipping: [localhost][0m

TASK [lmco.bundler.bundler_common : Validating arguments against arg spec 'main' - Defines required bundler variables for bundler common] ***
Monday 25 August 2025  06:52:47 +0000 (0:00:00.030)       0:00:01.090 ********* 
[0;32mok: [localhost][0m

TASK [lmco.bundler.bundler_common : Display Bundler Inputs] ********************
Monday 25 August 2025  06:52:47 +0000 (0:00:00.013)       0:00:01.103 ********* 
[0;36mskipping: [localhost][0m

TASK [lmco.bundler.bundler_common : One time set of ocp variables] *************
Monday 25 August 2025  06:52:49 +0000 (0:00:01.135)       0:00:02.238 ********* 
[0;32mok: [localhost][0m

TASK [Default bundler creation] ************************************************
Monday 25 August 2025  06:52:49 +0000 (0:00:00.520)       0:00:02.759 ********* 
[0;36mskipping: [localhost][0m

TASK [SNO bundler creation] ****************************************************
Monday 25 August 2025  06:52:49 +0000 (0:00:00.013)       0:00:02.772 ********* 
[0;36mskipping: [localhost][0m

TASK [AWS bundler creation] ****************************************************
Monday 25 August 2025  06:52:49 +0000 (0:00:00.011)       0:00:02.784 ********* 

TASK [lmco.bundler.bundler_common : Validating arguments against arg spec 'main' - Defines required bundler variables for bundler common] ***
Monday 25 August 2025  06:52:49 +0000 (0:00:00.045)       0:00:02.829 ********* 
[0;32mok: [localhost][0m

TASK [lmco.bundler.bundler_common : Display Bundler Inputs] ********************
Monday 25 August 2025  06:52:49 +0000 (0:00:00.012)       0:00:02.842 ********* 
[0;36mskipping: [localhost][0m

TASK [lmco.bundler.bundler_common : One time set of ocp variables] *************
Monday 25 August 2025  06:52:50 +0000 (0:00:01.134)       0:00:03.977 ********* 
[0;32mok: [localhost][0m

TASK [aws | Variable check] ****************************************************
Monday 25 August 2025  06:52:51 +0000 (0:00:00.516)       0:00:04.494 ********* 

TASK [lmco.galaxy.galaxy_common : variable_check | Check variables are defined] ***
Monday 25 August 2025  06:52:51 +0000 (0:00:00.023)       0:00:04.517 ********* 
[1;35m[WARNING]: conditional statements should not include jinja2 templating[0m
[1;35mdelimiters such as {{ }} or {% %}. Found: ({{ var_to_check }}) | default(none)[0m
[1;35m[WARNING]: conditional statements should not include jinja2 templating[0m
[1;35mdelimiters such as {{ }} or {% %}. Found: ({{ var_to_check }})[0m
[0;32mok: [localhost] => (item=bundler.nexus_storage is defined as nexus-storage.tar)[0m
[0;32mok: [localhost] => (item=bundler.verson_vars_file is defined as version_vars.yml)[0m
[0;32mok: [localhost] => (item=bundler.openshift_install_file is defined as openshift-install)[0m
[0;32mok: [localhost] => (item=bundler.openshift_version is defined as 4.14.31)[0m
[0;32mok: [localhost] => (item=bundler.redhat_registry_pull_secret_file or proxying_mirror is defined as True)[0m
[0;32mok: [localhost] => (item=bundler.rhel_iso_name is defined as rhel-9.4-x86_64-dvd.iso)[0m

TASK [lmco.bundler.bundler_main : aws | Setup container galaxy folder link to package files] ***
Monday 25 August 2025  06:52:51 +0000 (0:00:00.093)       0:00:04.610 ********* 
[0;36mincluded: /home/<USER>/.ansible/collections/ansible_collections/lmco/bundler/roles/bundler_main/tasks/setup_container_galaxy_folder_link.yml for localhost[0m

TASK [lmco.bundler.bundler_main : setup_container_galaxy_folder_link | Open permissions on opt] ***
Monday 25 August 2025  06:52:51 +0000 (0:00:00.019)       0:00:04.630 ********* 
[0;33mchanged: [localhost][0m

TASK [lmco.bundler.bundler_main : setup_container_galaxy_folder_link | Create directories] ***
Monday 25 August 2025  06:52:51 +0000 (0:00:00.325)       0:00:04.955 ********* 
[0;33mchanged: [localhost] => (item=/opt/package_files/galaxy_files)[0m

TASK [lmco.bundler.bundler_main : setup_container_galaxy_folder_link | Link galaxy folder to package folder] ***
Monday 25 August 2025  06:52:52 +0000 (0:00:00.190)       0:00:05.146 ********* 
[0;33mchanged: [localhost][0m

TASK [lmco.bundler.bundler_main : aws | Download Assets] ***********************
Monday 25 August 2025  06:52:52 +0000 (0:00:00.205)       0:00:05.351 ********* 
[0;36mincluded: /home/<USER>/.ansible/collections/ansible_collections/lmco/bundler/roles/bundler_main/tasks/download_assets.yml for localhost[0m

TASK [lmco.bundler.bundler_main : download_assets | Include variables from roles.yml] ***
Monday 25 August 2025  06:52:52 +0000 (0:00:00.024)       0:00:05.376 ********* 
[0;32mok: [localhost] => (item={'name': 'galaxy_platform', 'target': 'aws', 'provider': 'redhat_openshift', 'profile': 'default', 'state': 'present'})[0m
[0;32mok: [localhost] => (item={'name': 'galaxy_platform', 'target': 'redhat_openshift', 'provider': 'base', 'profile': 'default', 'state': 'present'})[0m

TASK [lmco.bundler.bundler_main : download_assets | Append variables to an existing variable] ***
Monday 25 August 2025  06:52:52 +0000 (0:00:00.119)       0:00:05.496 ********* 
[0;36mskipping: [localhost] => (item={'name': 'galaxy_platform', 'target': 'aws', 'provider': 'redhat_openshift', 'profile': 'default', 'state': 'present'}) [0m
[0;32mok: [localhost] => (item={'name': 'galaxy_platform', 'target': 'redhat_openshift', 'provider': 'base', 'profile': 'default', 'state': 'present'})[0m

TASK [lmco.bundler.bundler_main : download_assets | Create a list of role names] ***
Monday 25 August 2025  06:52:52 +0000 (0:00:00.083)       0:00:05.580 ********* 
[0;32mok: [localhost] => (item={'name': 'lmco.openshift.ocp_configuration', 'software_version': 'default'})[0m
[0;32mok: [localhost] => (item={'name': 'lmco.openshift.ocp_etcd_backup', 'tags': 'install', 'software_version': 'default'})[0m

TASK [lmco.bundler.bundler_main : download_assets | Assets main.yml file location - _assets/tasks/main.yml] ***
Monday 25 August 2025  06:52:52 +0000 (0:00:00.128)       0:00:05.708 ********* 
[0;32mok: [localhost] => (item=lmco.openshift.ocp_configuration)[0m
[0;32mok: [localhost] => (item=lmco.openshift.ocp_etcd_backup)[0m

TASK [lmco.bundler.bundler_main : download_assets | Check if the template files exists - _assets/task/main.yml] ***
Monday 25 August 2025  06:52:52 +0000 (0:00:00.131)       0:00:05.840 ********* 
[0;36mskipping: [localhost] => (item=/home/<USER>/.ansible/collections/ansible_collections/lmco/openshift/roles/ocp_configuration/tasks/main.yml) [0m
[0;36mskipping: [localhost] => (item=/home/<USER>/.ansible/collections/ansible_collections/lmco/openshift/roles/ocp_etcd_backup/tasks/main.yml) [0m
[0;36mskipping: [localhost][0m

TASK [lmco.bundler.bundler_main : download_assets | Convert Jinja2 templates to JSON - _assets/task/main.yml] ***
Monday 25 August 2025  06:52:52 +0000 (0:00:00.023)       0:00:05.863 ********* 
[0;36mskipping: [localhost] => (item={'changed': False, 'skipped': True, 'skip_reason': 'Conditional result was False', 'false_condition': "'_assets/tasks/main.yml' in item", 'item': '/home/<USER>/.ansible/collections/ansible_collections/lmco/openshift/roles/ocp_configuration/tasks/main.yml', 'ansible_loop_var': 'item'}) [0m
[0;36mskipping: [localhost] => (item={'changed': False, 'skipped': True, 'skip_reason': 'Conditional result was False', 'false_condition': "'_assets/tasks/main.yml' in item", 'item': '/home/<USER>/.ansible/collections/ansible_collections/lmco/openshift/roles/ocp_etcd_backup/tasks/main.yml', 'ansible_loop_var': 'item'}) [0m
[0;36mskipping: [localhost][0m

TASK [lmco.bundler.bundler_main : download_assets | Combine JSON data - asset_role_list] ***
Monday 25 August 2025  06:52:52 +0000 (0:00:00.022)       0:00:05.885 ********* 
[0;36mskipping: [localhost] => (item=0) [0m
[0;36mskipping: [localhost] => (item=1) [0m
[0;36mskipping: [localhost][0m

TASK [lmco.bundler.bundler_main : download_assets | Combine JSON data - asset_role_list] ***
Monday 25 August 2025  06:52:52 +0000 (0:00:00.036)       0:00:05.922 ********* 
[0;32mok: [localhost][0m

TASK [lmco.bundler.bundler_main : download_assets | Get each assets role that exists] ***
Monday 25 August 2025  06:52:52 +0000 (0:00:00.075)       0:00:05.997 ********* 
[0;36mskipping: [localhost][0m

TASK [download_assets | Include asset management roles] ************************
Monday 25 August 2025  06:52:52 +0000 (0:00:00.011)       0:00:06.008 ********* 
[0;36mskipping: [localhost][0m

TASK [lmco.bundler.bundler_main : aws | Setup container] ***********************
Monday 25 August 2025  06:52:52 +0000 (0:00:00.016)       0:00:06.025 ********* 
[0;36mincluded: /home/<USER>/.ansible/collections/ansible_collections/lmco/bundler/roles/bundler_main/tasks/setup_container.yml for localhost[0m

TASK [lmco.bundler.bundler_main : setup_container | Create /etc/hosts copy] ****
Monday 25 August 2025  06:52:52 +0000 (0:00:00.020)       0:00:06.046 ********* 
[0;33mchanged: [localhost][0m

TASK [lmco.bundler.bundler_main : setup_container | Replace a localhost entry searching for a literal string to avoid escaping] ***
Monday 25 August 2025  06:52:53 +0000 (0:00:00.513)       0:00:06.559 ********* 
[0;33mchanged: [localhost][0m

TASK [lmco.bundler.bundler_main : setup_container | Update /etc/hosts] *********
Monday 25 August 2025  06:52:53 +0000 (0:00:00.308)       0:00:06.868 ********* 
[0;33mchanged: [localhost][0m

TASK [lmco.bundler.bundler_main : setup_container | Create directories] ********
Monday 25 August 2025  06:52:54 +0000 (0:00:00.289)       0:00:07.158 ********* 
[0;33mchanged: [localhost] => (item=/home/<USER>/.docker)[0m
[0;33mchanged: [localhost] => (item=/opt/nexus_files)[0m
[0;32mok: [localhost] => (item=/opt/package_files/galaxy_files)[0m
[0;33mchanged: [localhost] => (item=/opt/openshift/mirror-redhat2galaxy)[0m
[0;33mchanged: [localhost] => (item=/home/<USER>/.local/bin)[0m

TASK [lmco.bundler.bundler_main : aws | Download Bundle Files] *****************
Monday 25 August 2025  06:52:54 +0000 (0:00:00.899)       0:00:08.058 ********* 
[0;36mincluded: /home/<USER>/.ansible/collections/ansible_collections/lmco/bundler/roles/bundler_main/tasks/download_bundle_files.yml for localhost[0m

TASK [lmco.bundler.bundler_main : download_bundle_files | Download lmco.galaxy image as lmco.galaxy.tar image name - harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest] ***
Monday 25 August 2025  06:52:54 +0000 (0:00:00.053)       0:00:08.112 ********* 
[0;31mfatal: [localhost]: FAILED! => changed=true [0m
[0;31m  cmd: |-[0m
[0;31m    skopeo copy docker://harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest docker-archive:/opt/package_files/lmco.galaxy.tar[0m
[0;31m  delta: '0:00:00.113283'[0m
[0;31m  end: '2025-08-25 06:52:55.255616'[0m
[0;31m  msg: non-zero return code[0m
[0;31m  rc: 1[0m
[0;31m  start: '2025-08-25 06:52:55.142333'[0m
[0;31m  stderr: 'time="2025-08-25T06:52:55Z" level=fatal msg="initializing source docker://harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest: pinging container registry harbor.global.lmco.com: Get \"https://harbor.global.lmco.com/v2/\": dial tcp 127.0.0.1:443: connect: connection refused"'[0m
[0;31m  stderr_lines: <omitted>[0m
[0;31m  stdout: ''[0m
[0;31m  stdout_lines: <omitted>[0m

PLAY RECAP *********************************************************************
[0;31mlocalhost[0m                  : [0;32mok=24  [0m [0;33mchanged=7   [0m unreachable=0    [0;31mfailed=1   [0m [0;36mskipped=10  [0m rescued=0    ignored=0   

Monday 25 August 2025  06:52:55 +0000 (0:00:00.310)       0:00:08.422 ********* 
=============================================================================== 
lmco.bundler.bundler_common : Display Bundler Inputs -------------------- 1.14s
lmco.bundler.bundler_common : Display Bundler Inputs -------------------- 1.13s
lmco.bundler.bundler_main : setup_container | Create directories -------- 0.90s
Gathering Facts --------------------------------------------------------- 0.87s
lmco.bundler.bundler_common : One time set of ocp variables ------------- 0.52s
lmco.bundler.bundler_common : One time set of ocp variables ------------- 0.52s
lmco.bundler.bundler_main : setup_container | Create /etc/hosts copy ---- 0.51s
lmco.bundler.bundler_main : setup_container_galaxy_folder_link | Open permissions on opt --- 0.33s
lmco.bundler.bundler_main : download_bundle_files | Download lmco.galaxy image as lmco.galaxy.tar image name - harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest --- 0.31s
lmco.bundler.bundler_main : setup_container | Replace a localhost entry searching for a literal string to avoid escaping --- 0.31s
lmco.bundler.bundler_main : setup_container | Update /etc/hosts --------- 0.29s
lmco.bundler.bundler_main : setup_container_galaxy_folder_link | Link galaxy folder to package folder --- 0.21s
lmco.bundler.bundler_main : setup_container_galaxy_folder_link | Create directories --- 0.19s
lmco.bundler.bundler_main : download_assets | Assets main.yml file location - _assets/tasks/main.yml --- 0.13s
lmco.bundler.bundler_main : download_assets | Create a list of role names --- 0.13s
lmco.bundler.bundler_main : download_assets | Include variables from roles.yml --- 0.12s
lmco.galaxy.galaxy_common : variable_check | Check variables are defined --- 0.09s
lmco.bundler.bundler_main : download_assets | Append variables to an existing variable --- 0.08s
lmco.bundler.bundler_main : download_assets | Combine JSON data - asset_role_list --- 0.08s
lmco.galaxy.galaxy_common : Validating arguments against arg spec 'main' - Galaxy Common Role --- 0.05s
lmco.bundler.bundler_main : aws | Download Bundle Files ----------------- 0.05s
AWS bundler creation ---------------------------------------------------- 0.05s
lmco.galaxy.galaxy_common : Generate control_dict ----------------------- 0.04s
lmco.bundler.bundler_main : download_assets | Combine JSON data - asset_role_list --- 0.04s
Common bundler ---------------------------------------------------------- 0.04s
lmco.galaxy.galaxy_common : Display Galaxy Inputs ----------------------- 0.03s
lmco.bundler.bundler_main : aws | Download Assets ----------------------- 0.02s
lmco.bundler.bundler_main : download_assets | Check if the template files exists - _assets/task/main.yml --- 0.02s
aws | Variable check ---------------------------------------------------- 0.02s
lmco.bundler.bundler_main : download_assets | Convert Jinja2 templates to JSON - _assets/task/main.yml --- 0.02s
lmco.bundler.bundler_main : aws | Setup container ----------------------- 0.02s
lmco.bundler.bundler_main : aws | Setup container galaxy folder link to package files --- 0.02s
download_assets | Include asset management roles ------------------------ 0.02s
lmco.bundler.bundler_common : Validating arguments against arg spec 'main' - Defines required bundler variables for bundler common --- 0.01s
Default bundler creation ------------------------------------------------ 0.01s
lmco.bundler.bundler_common : Validating arguments against arg spec 'main' - Defines required bundler variables for bundler common --- 0.01s
SNO bundler creation ---------------------------------------------------- 0.01s
lmco.bundler.bundler_main : download_assets | Get each assets role that exists --- 0.01s

[0;33mWARNING: Event retrieved from the cluster: 0/17 nodes are available: waiting for ephemeral volume controller to create the persistentvolumeclaim "runner-kzt5roj16-project-13816-concurrent-0-xlb3upex-containers". preemption: 0/17 nodes are available: 17 Preemption is not helpful for scheduling.[0;m
section_end:1756104775:step_script
[0Ksection_start:1756104775:cleanup_file_variables
[0K[0K[36;1mCleaning up project directory and file based variables[0;m[0;m

section_end:1756104776:cleanup_file_variables
[0K[31;1mERROR: Job failed: command terminated with exit code 1