---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        feature-branch-deploy.yml                                        #
# Version:                                                                        #
#               2025-01-25 Feature Branch Testing                                 #
# Create Date:  2025-01-25                                                        #
# Author:       Feature Branch Testing                                            #
# Description:                                                                    #
#               Pipeline configuration for testing feature branch changes         #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

# Include the base ansible collection configuration
include:
  - /gitlab/ansible-collection.yml

variables:
  GIT_STRATEGY: clone
  ENABLE_SAST_SCANS: "false"
  GALAXY_VERSION: "dev-$(date +%Y%m%d-%H%M%S)"
  BUILDER_LMI_IP: **************
  BUILDER_MGMT_IP: **************
  BUILDER_USER: galaxy
  STORAGE_BACKEND: nexus
  RESOURCE_GROUP: "NULL_GROUP"
  GALAXY_FOLDER: /data/${CLUSTER_NAME}-bundle-cicd-install
  HTTP_PORT: 8090
  VERBOSITY: ""
  ADDITIONAL_FILES: "$CI_PROJECT_DIR/host_vars,$CI_PROJECT_DIR/group_vars,$CI_PROJECT_DIR/files,$CI_PROJECT_DIR/inventory.yml"
  OPENSHIFT_VARS: "$CI_PROJECT_DIR/group_vars/openshift.yml"
  # Feature branch specific variables
  FEATURE_ROLES_PATH: "$CI_PROJECT_DIR/roles"  # Path to your feature branch roles

generate_galaxy_bundle_feature:
  image: harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:$GALAXY_VERSION
  stage: build
  timeout: 12h
  variables:
    ANSIBLE_FORCE_COLOR: "true"
  rules:
    # Only run on feature branches or when manually triggered
    - if: $CI_COMMIT_REF_NAME != "main" && $CI_PIPELINE_SOURCE == "push"
      when: always
    - when: manual
      allow_failure: true
  before_script:
    - echo "Setting up registry authentication and network configuration for feature branch"
    # Backup original /etc/hosts before any modifications
    - cp /etc/hosts /etc/hosts.backup
    # Ensure Harbor registry resolves correctly (not to localhost)
    - echo "Checking Harbor registry DNS resolution"
    - nslookup harbor.global.lmco.com || echo "DNS lookup failed, will use IP"
    # Create Docker config directory and authenticate
    - mkdir -p ~/.docker
    - echo "Authenticating with Harbor registry using skopeo"
    - skopeo login harbor.global.lmco.com --username ${HARBOR_US_USER} --password ${HARBOR_US_USER_CLI_SECRET}
    # Create Docker config.json for additional authentication
    - echo "{\"auths\":{\"harbor.global.lmco.com\":{\"auth\":\"$(echo -n ${HARBOR_US_USER}:${HARBOR_US_USER_CLI_SECRET} | base64 -w 0)\"}}}" > ~/.docker/config.json
    # Test registry connectivity before proceeding
    - echo "Testing registry connectivity"
    - skopeo inspect docker://harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:${GALAXY_VERSION} --raw || echo "Registry inspection failed"
    
    # Install feature branch roles
    - echo "Installing feature branch roles from $FEATURE_ROLES_PATH"
    - mkdir -p ~/.ansible/collections/ansible_collections/lmco/
    - rm -rf ~/.ansible/collections/ansible_collections/lmco/bundler
    - cp -r $FEATURE_ROLES_PATH ~/.ansible/collections/ansible_collections/lmco/bundler
    - echo "Feature branch roles installed successfully"
    - ls -la ~/.ansible/collections/ansible_collections/lmco/bundler/roles/
    
  script:
    - welcome
    - env
    # Create a custom /etc/hosts backup and protection script
    - |
      echo "Setting up /etc/hosts protection for feature branch testing..."
      cp /etc/hosts /etc/hosts.original

      # Create a script that prevents Harbor from being added to localhost
      cat > /tmp/protect_hosts.sh << 'EOF'
      #!/bin/bash
      # This script monitors /etc/hosts and removes harbor.global.lmco.com from localhost line
      while true; do
        if grep -q "127.0.0.1.*harbor\.global\.lmco\.com" /etc/hosts; then
          echo "$(date): Detected Harbor registry in localhost line, removing..."
          # Remove harbor.global.lmco.com from the localhost line
          sed -i '/^127\.0\.0\.1/s/harbor\.global\.lmco\.com//g' /etc/hosts
          # Clean up multiple spaces
          sed -i '/^127\.0\.0\.1/s/  */ /g' /etc/hosts
          # Remove trailing spaces
          sed -i '/^127\.0\.0\.1/s/ *$//' /etc/hosts
          echo "$(date): Fixed /etc/hosts"
        fi
        sleep 2
      done
      EOF
      chmod +x /tmp/protect_hosts.sh

      # Start the protection script in background
      /tmp/protect_hosts.sh &
      PROTECT_PID=$!
      echo "Started /etc/hosts protection with PID: $PROTECT_PID"

    # Run the main bundle creation with DNS protection overrides and feature branch roles
    - echo "Running bundle creation with feature branch roles..."
    - >
      ansible-playbook -i localhost lmco.bundler.create_bundle.yml
      -e "@$OPENSHIFT_VARS"
      -e "{'cli_galaxy':{'version':'${GALAXY_VERSION}'}}"
      -e "{'bundle_name':'${CLUSTER_NAME}' }"
      -e "{'cli_bundler':{'additional_files':'${ADDITIONAL_FILES}'}}"
      -e "bundler={'default_registry_url':'galaxy-registry.local','default_nexus_url':'galaxy-nexus.local','nexus_domain':'galaxy-nexus.apps.galaxy-uge1-ocp02.us.lmco.com'}"
      -e "apps_install_path=/opt/package_files"
      -e "proxying_mirror=false"
      -e "domain_mappings=[]"
      $VERBOSITY

    # Clean up the protection script
    - |
      echo "Cleaning up /etc/hosts protection..."
      pkill -f "protect_hosts.sh" || true
      cp /etc/hosts.original /etc/hosts || true
      echo "Feature branch testing cleanup completed"
  tags:
    - aws
    - galaxy-xl
    - kubernetes
  artifacts:
    when: always
    expire_in: 1 week
    paths:
      - "*.log"
      - "deploy-*.out"
    reports:
      junit: "test-results.xml"
