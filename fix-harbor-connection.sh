#!/bin/bash

# Quick fix script for Harbor registry connection issues
# Run this script before your Ansible playbook execution

echo "=== Harbor Registry Connection Fix ==="
echo "Date: $(date)"
echo

# Step 1: Fix /etc/hosts entries
echo "1. Fixing /etc/hosts entries..."
echo "   Current Harbor entries in /etc/hosts:"
grep -n "harbor.global.lmco.com" /etc/hosts || echo "   No Harbor entries found"

# Remove any localhost mappings for Harbor
echo "   Removing localhost mappings for Harbor registry..."
sudo sed -i.backup '/^127\.0\.0\.1.*harbor\.global\.lmco\.com/d' /etc/hosts
sudo sed -i '/^::1.*harbor\.global\.lmco\.com/d' /etc/hosts

echo "   ✅ /etc/hosts cleaned up"

# Step 2: Set up proxy environment
echo
echo "2. Setting up proxy environment..."
export HTTP_PROXY="http://proxy-zsgov.external.lmco.com:80"
export HTTPS_PROXY="http://proxy-zsgov.external.lmco.com:80"
export NO_PROXY=".lmco.com,localhost,127.0.0.1"

echo "   HTTP_PROXY: $HTTP_PROXY"
echo "   HTTPS_PROXY: $HTTPS_PROXY"
echo "   NO_PROXY: $NO_PROXY"
echo "   ✅ Proxy environment configured"

# Step 3: Test DNS resolution
echo
echo "3. Testing DNS resolution..."
if nslookup harbor.global.lmco.com >/dev/null 2>&1; then
    echo "   ✅ DNS resolution working"
    RESOLVED_IP=$(nslookup harbor.global.lmco.com | grep -A1 "Name:" | tail -1 | awk '{print $2}')
    echo "   Harbor registry resolves to: $RESOLVED_IP"
else
    echo "   ❌ DNS resolution failed"
    echo "   This might cause connection issues"
fi

# Step 4: Test network connectivity
echo
echo "4. Testing network connectivity..."

# Test direct connection
echo "   Testing direct connection..."
if curl -s -I --connect-timeout 10 https://harbor.global.lmco.com/v2/ | head -1 | grep -q "HTTP"; then
    echo "   ✅ Direct connection works"
    DIRECT_WORKS=true
else
    echo "   ❌ Direct connection failed"
fi

# Test proxy connection
echo "   Testing proxy connection..."
if curl -s -I --proxy "$HTTP_PROXY" --connect-timeout 10 https://harbor.global.lmco.com/v2/ | head -1 | grep -q "HTTP"; then
    echo "   ✅ Proxy connection works"
    PROXY_WORKS=true
else
    echo "   ❌ Proxy connection failed"
fi

# Step 5: Test skopeo connectivity
echo
echo "5. Testing skopeo connectivity..."
if command -v skopeo >/dev/null 2>&1; then
    echo "   ✅ skopeo is available"
    
    # Test with proxy
    echo "   Testing skopeo with proxy..."
    if HTTP_PROXY="$HTTP_PROXY" HTTPS_PROXY="$HTTPS_PROXY" NO_PROXY="$NO_PROXY" \
       timeout 30 skopeo inspect docker://harbor.global.lmco.com/v2/ --raw >/dev/null 2>&1; then
        echo "   ✅ skopeo proxy connection works"
        SKOPEO_WORKS=true
    else
        echo "   ❌ skopeo proxy connection failed"
    fi
    
    # Test authentication if credentials are available
    if [ -n "$HARBOR_US_USER" ] && [ -n "$HARBOR_US_USER_CLI_SECRET" ]; then
        echo "   Testing skopeo authentication..."
        if HTTP_PROXY="$HTTP_PROXY" HTTPS_PROXY="$HTTPS_PROXY" NO_PROXY="$NO_PROXY" \
           timeout 30 skopeo login harbor.global.lmco.com --username "$HARBOR_US_USER" --password "$HARBOR_US_USER_CLI_SECRET" 2>/dev/null; then
            echo "   ✅ skopeo authentication successful"
            AUTH_WORKS=true
        else
            echo "   ❌ skopeo authentication failed"
        fi
    else
        echo "   ⚠️  Harbor credentials not set (HARBOR_US_USER, HARBOR_US_USER_CLI_SECRET)"
    fi
else
    echo "   ❌ skopeo not available"
fi

# Step 6: Create Docker config if credentials are available
echo
echo "6. Setting up Docker authentication..."
if [ -n "$HARBOR_US_USER" ] && [ -n "$HARBOR_US_USER_CLI_SECRET" ]; then
    mkdir -p ~/.docker
    
    # Create Docker config.json
    AUTH_STRING=$(echo -n "$HARBOR_US_USER:$HARBOR_US_USER_CLI_SECRET" | base64 -w 0 2>/dev/null || echo -n "$HARBOR_US_USER:$HARBOR_US_USER_CLI_SECRET" | base64)
    
    cat > ~/.docker/config.json << EOF
{
  "auths": {
    "harbor.global.lmco.com": {
      "auth": "$AUTH_STRING"
    }
  },
  "proxies": {
    "default": {
      "httpProxy": "$HTTP_PROXY",
      "httpsProxy": "$HTTPS_PROXY",
      "noProxy": "$NO_PROXY"
    }
  }
}
EOF
    
    chmod 600 ~/.docker/config.json
    echo "   ✅ Docker config.json created with authentication and proxy settings"
else
    echo "   ⚠️  Skipping Docker config creation (credentials not available)"
fi

# Step 7: Summary and recommendations
echo
echo "=== Summary ==="
if [ "$PROXY_WORKS" = true ] || [ "$DIRECT_WORKS" = true ]; then
    echo "✅ Network connectivity: WORKING"
else
    echo "❌ Network connectivity: FAILED"
fi

if [ "$SKOPEO_WORKS" = true ]; then
    echo "✅ Skopeo connectivity: WORKING"
else
    echo "❌ Skopeo connectivity: FAILED"
fi

if [ "$AUTH_WORKS" = true ]; then
    echo "✅ Authentication: WORKING"
else
    echo "❌ Authentication: NEEDS ATTENTION"
fi

echo
echo "=== Recommendations ==="
if [ "$PROXY_WORKS" != true ] && [ "$DIRECT_WORKS" != true ]; then
    echo "❌ CRITICAL: No network connectivity to Harbor registry"
    echo "   - Check if proxy server is accessible"
    echo "   - Verify firewall rules"
    echo "   - Contact network administrator"
elif [ "$SKOPEO_WORKS" != true ]; then
    echo "⚠️  WARNING: Skopeo cannot connect to Harbor registry"
    echo "   - This will cause the same error in your GitLab CI"
    echo "   - Check proxy configuration in your environment"
    echo "   - Verify skopeo proxy support"
elif [ "$AUTH_WORKS" != true ]; then
    echo "⚠️  WARNING: Authentication issues detected"
    echo "   - Verify HARBOR_US_USER and HARBOR_US_USER_CLI_SECRET"
    echo "   - Check if credentials are valid"
    echo "   - Test manual login: skopeo login harbor.global.lmco.com"
else
    echo "✅ All tests passed! Your GitLab CI should work now."
fi

echo
echo "=== Next Steps ==="
echo "1. If tests passed: Commit your Ansible role changes and run GitLab CI"
echo "2. If tests failed: Address the issues above before running GitLab CI"
echo "3. Monitor GitLab CI logs for the connectivity test output"

echo
echo "=== Environment Variables for GitLab CI ==="
echo "Make sure these are set in your GitLab CI environment:"
echo "- HARBOR_US_USER"
echo "- HARBOR_US_USER_CLI_SECRET"
echo "- HTTP_PROXY (optional, will use default)"
echo "- HTTPS_PROXY (optional, will use default)"

echo
echo "=== Fix Complete ==="
